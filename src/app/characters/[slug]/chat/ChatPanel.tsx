"use client";

import { FormEvent, useEffect, useMemo, useRef, useState } from "react";
import ReactMarkdown from "react-markdown";
import type { Components } from "react-markdown";
import remarkGfm from "remark-gfm";

import { rehypeHighlightQuotes } from "@/lib/markdown/rehypeHighlightQuotes";

type Role = "assistant" | "user";

type ChatMessage = {
  id: string;
  role: Role;
  content: string;
  createdAt?: string;
};

type ChatModelOption = {
  id: string;
  identifier: string;
  displayName: string;
  providerName: string;
  currency: string;
  promptPricePer1MTokens: number | null;
  completionPricePer1MTokens: number | null;
  isDefault: boolean;
};

type ChatSessionData = {
  id: string;
  messages: ChatMessage[];
  model: {
    id: string;
    displayName: string;
  } | null;
};

type SessionSummary = {
  id: string;
  createdAt: string;
  updatedAt: string;
  model: {
    id: string;
    displayName: string;
  } | null;
  messageCount: number;
  lastMessagePreview: string | null;
};

type ChatCharacter = {
  id: string;
  slug: string;
  name: string;
  characterVersion: string;
  avatarUrl: string | null;
  firstMessage: string | null;
  alternateGreetings: string[];
  creatorNotes: string | null;
  description: string | null;
  scenario: string | null;
  tags: string[];
};

type ChatPanelProps = {
  character: ChatCharacter;
  initialChat: ChatSessionData | null;
  initialSessions: SessionSummary[];
  initialIncludeBook: boolean;
  models: ChatModelOption[];
  initialModelId: string | null;
};

type ApiResponse = {
  message?: {
    role: Role;
    content: string;
  };
  error?: string;
  details?: string;
};

type StreamChunk =
  | { type: "delta"; content: string }
  | { type: "done" }
  | { type: "error"; message?: string; details?: string };

type ApiChatSession = {
  id: string;
  createdAt: string;
  updatedAt: string;
  model?: {
    id: string;
    displayName: string;
  } | null;
  messages: Array<{
    id: string;
    role: Role;
    content: string;
    createdAt?: string;
  }>;
};

const createMessageId = () => `${Date.now()}-${Math.random().toString(16).slice(2)}`;

const mapMessagesForRequest = (messages: ChatMessage[]) =>
  messages.map((message) => ({
    role: message.role,
    content: message.content,
  }));

const assistantMarkdownComponents: Components = {
  root: ({ children }) => (
    <div className="space-y-2 whitespace-pre-wrap text-sm leading-relaxed text-neutral-900">
      {children}
    </div>
  ),
  p: ({ children }) => (
    <p className="text-sm leading-relaxed text-neutral-900">{children}</p>
  ),
  strong: ({ children }) => <strong className="font-semibold">{children}</strong>,
  em: ({ children }) => <em className="italic">{children}</em>,
  a: ({ children, href }) => (
    <a
      href={href ?? "#"}
      className="text-sky-600 underline underline-offset-2"
      target="_blank"
      rel="noreferrer"
    >
      {children}
    </a>
  ),
  code({ children, inline }) {
    if (inline) {
      return (
        <code className="rounded bg-neutral-900/90 px-1 py-0.5 text-[0.78rem] text-neutral-100">
          {children}
        </code>
      );
    }

    return (
      <pre className="whitespace-pre-wrap break-words rounded-md bg-neutral-900 px-3 py-2 text-[0.78rem] text-neutral-100">
        <code className="font-mono">{children}</code>
      </pre>
    );
  },
  blockquote: ({ children }) => (
    <blockquote className="border-l-2 border-neutral-300 pl-4 text-sm italic text-neutral-700">
      {children}
    </blockquote>
  ),
  ul: ({ children }) => (
    <ul className="ml-5 list-disc space-y-1 text-sm text-neutral-900">{children}</ul>
  ),
  ol: ({ children }) => (
    <ol className="ml-5 list-decimal space-y-1 text-sm text-neutral-900">{children}</ol>
  ),
  li: ({ children }) => <li className="leading-relaxed">{children}</li>,
  h1: ({ children }) => (
    <h1 className="text-lg font-semibold text-neutral-900">{children}</h1>
  ),
  h2: ({ children }) => (
    <h2 className="text-base font-semibold text-neutral-900">{children}</h2>
  ),
  h3: ({ children }) => (
    <h3 className="text-sm font-semibold text-neutral-900">{children}</h3>
  ),
  hr: () => <hr className="my-4 border-neutral-200" />,
};

const toChatMessages = (messages: ApiChatSession["messages"]): ChatMessage[] =>
  messages.map((message) => ({
    id: String(message.id),
    role: message.role,
    content: message.content,
    createdAt: message.createdAt,
  }));

const sortSummaries = (items: SessionSummary[]) =>
  [...items].sort(
    (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
  );

const formatSessionLabel = (summary: SessionSummary) => {
  const updated = new Date(summary.updatedAt);
  const parts = [`${summary.messageCount} messages`];
  if (summary.model?.displayName) {
    parts.push(summary.model.displayName);
  }
  return `${updated.toLocaleString()} (${parts.join(", ")})`;
};

export default function ChatPanel({
  character,
  initialChat,
  initialSessions,
  initialIncludeBook,
  models,
  initialModelId,
}: ChatPanelProps) {
  const greetings = useMemo(() => {
    const list: string[] = [];
    if (character.firstMessage && character.firstMessage.trim().length > 0) {
      list.push(character.firstMessage.trim());
    }

    if (character.alternateGreetings?.length) {
      for (const greeting of character.alternateGreetings) {
        if (typeof greeting === "string" && greeting.trim().length > 0) {
          list.push(greeting.trim());
        }
      }
    }

    return list;
  }, [character]);

  const defaultModelId = useMemo(() => {
    const defaultModel = models.find((model) => model.isDefault);
    if (defaultModel) {
      return defaultModel.id;
    }

    return models[0]?.id ?? null;
  }, [models]);

  const [selectedModelId, setSelectedModelId] = useState<string | null>(
    () => initialModelId ?? defaultModelId,
  );

  useEffect(() => {
    if (!selectedModelId && defaultModelId) {
      setSelectedModelId(defaultModelId);
    }
  }, [defaultModelId, selectedModelId]);

  const selectedModel = useMemo(
    () => models.find((model) => model.id === selectedModelId) ?? null,
    [models, selectedModelId],
  );

  const hasModels = models.length > 0;

  const [chatSessionId, setChatSessionId] = useState<string | null>(
    initialChat?.id ?? null,
  );
  const resolvedInitialGreetingIndex = useMemo(() => {
    if (initialChat?.messages?.length) {
      const firstMessage = initialChat.messages[0]?.content?.trim();
      if (firstMessage) {
        const matchIndex = greetings.findIndex(
          (greeting) => greeting === firstMessage,
        );
        if (matchIndex >= 0) {
          return matchIndex;
        }
      }
    }

    return 0;
  }, [initialChat, greetings]);

  const [greetingIndex, setGreetingIndex] = useState(resolvedInitialGreetingIndex);
  const [messages, setMessages] = useState<ChatMessage[]>(() => {
    if (initialChat) {
      return initialChat.messages;
    }

    const greeting = greetings[greetingIndex];

    if (!greeting) {
      return [];
    }

    return [
      {
        id: "assistant-preview",
        role: "assistant",
        content: greeting,
      },
    ];
  });
  const [sessions, setSessions] = useState<SessionSummary[]>(() =>
    sortSummaries(initialSessions),
  );
  const [includeBook, setIncludeBook] = useState<boolean>(initialIncludeBook);
  const [input, setInput] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSessionLoading, setIsSessionLoading] = useState(false);
  const [isMessageUpdating, setIsMessageUpdating] = useState(false);
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editingContent, setEditingContent] = useState("");
  const [error, setError] = useState<string | null>(null);

  const listRef = useRef<HTMLDivElement | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const readerRef = useRef<ReadableStreamDefaultReader<Uint8Array> | null>(null);

  useEffect(() => {
    if (listRef.current) {
      listRef.current.scrollTop = listRef.current.scrollHeight;
    }
  }, [messages]);

  useEffect(() => {
    if (chatSessionId) {
      return;
    }

    const greeting = greetings[greetingIndex];

    if (!greeting) {
      setMessages([]);
      return;
    }

    setMessages([
      {
        id: "assistant-preview",
        role: "assistant",
        content: greeting,
      },
    ]);
  }, [greetingIndex, chatSessionId, greetings]);

  const upsertSessionSummary = (summary: SessionSummary) => {
    setSessions((current) =>
      sortSummaries([
        summary,
        ...current.filter((item) => item.id !== summary.id),
      ]),
    );
  };

  const removeSessionSummary = (sessionId: string) => {
    setSessions((current) => current.filter((session) => session.id !== sessionId));
  };

  const resolveSessionSummary = (
    payload: ApiChatSession,
    messagesSnapshot: ChatMessage[],
  ): SessionSummary => ({
    id: String(payload.id),
    createdAt: payload.createdAt,
    updatedAt: payload.updatedAt,
    model: payload.model
      ? { id: payload.model.id, displayName: payload.model.displayName }
      : null,
    messageCount: messagesSnapshot.length,
    lastMessagePreview: messagesSnapshot.length
      ? messagesSnapshot[messagesSnapshot.length - 1]?.content ?? null
      : null,
  });

  const refreshSessionFromServer = async (sessionId: string) => {
    if (!sessionId) {
      return;
    }

    try {
      const response = await fetch(
        `/api/characters/${character.slug}/sessions/${sessionId}`,
      );

      const payload = await response.json();

      if (!response.ok || !payload?.chatSession) {
        return;
      }

      const sessionPayload = payload.chatSession as ApiChatSession;
      const sessionMessages = Array.isArray(sessionPayload.messages)
        ? toChatMessages(sessionPayload.messages)
        : [];

      setChatSessionId(String(sessionPayload.id));
      setMessages(sessionMessages);
      setEditingMessageId(null);
      setEditingContent("");

      if (sessionPayload.model?.id) {
        setSelectedModelId(sessionPayload.model.id);
      } else {
        setSelectedModelId((current) => current ?? defaultModelId);
      }

      upsertSessionSummary(resolveSessionSummary(sessionPayload, sessionMessages));
    } catch (refreshError) {
      console.error("Refresh chat session failed", refreshError);
    }
  };

type SendChatRequestParams = {
  session: ChatSessionData;
  requestMessages: ChatMessage[];
  revertMessages: () => void;
  restoreInputValue?: string;
  assistantMessageId?: string;
  revertOnAbortWhenEmpty?: boolean;
  reuseUserMessageId?: string | null;
  replaceAssistantMessageId?: string | null;
};

const sendChatRequest = async ({
  session,
  requestMessages,
  revertMessages,
  restoreInputValue,
  assistantMessageId,
  revertOnAbortWhenEmpty = false,
  reuseUserMessageId,
  replaceAssistantMessageId,
}: SendChatRequestParams): Promise<string | null> => {
    const requestSnapshot = requestMessages.map((message) => ({ ...message }));
    const assistantId = assistantMessageId ?? createMessageId();
    let accumulatedAssistantContent = "";

    if (!assistantMessageId) {
      setMessages((current) => [
        ...current,
        {
          id: assistantId,
          role: "assistant",
          content: "",
        },
      ]);
    } else {
      setMessages((current) =>
        current.map((message) =>
          message.id === assistantId ? { ...message, content: "" } : message,
        ),
      );
    }

    const updateAssistant = (content: string) => {
      setMessages((current) =>
        current.map((message) =>
          message.id === assistantId ? { ...message, content } : message,
        ),
      );
    };

    const removeAssistant = () => {
      if (!assistantMessageId) {
        setMessages((current) =>
          current.filter((message) => message.id !== assistantId),
        );
      }
    };

    try {
      const controller = new AbortController();
      abortControllerRef.current = controller;

      const payload: {
        chatSessionId: string;
        messages: ReturnType<typeof mapMessagesForRequest>;
        includeCharacterBook: boolean;
        modelId: string | null;
        reuseUserMessageId?: string;
        replaceAssistantMessageId?: string;
      } = {
        chatSessionId: session.id,
        messages: mapMessagesForRequest(requestSnapshot),
        includeCharacterBook: includeBook,
        modelId: selectedModelId,
      };

      if (reuseUserMessageId) {
        payload.reuseUserMessageId = reuseUserMessageId;
      }

      if (replaceAssistantMessageId) {
        payload.replaceAssistantMessageId = replaceAssistantMessageId;
      }

      const response = await fetch(`/api/characters/${character.slug}/chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
        signal: controller.signal,
      });

      if (!response.ok) {
        let payload: ApiResponse | undefined;
        try {
          payload = (await response.json()) as ApiResponse;
        } catch {
          // ignore JSON parse failures
        }

        removeAssistant();
        revertMessages();
        if (typeof restoreInputValue === "string") {
          setInput(restoreInputValue);
        }
        setError(payload?.error ?? "Chat request failed.");
        return null;
      }

      if (!response.body) {
        const payload = (await response.json()) as ApiResponse;

        if (!payload.message) {
          removeAssistant();
          revertMessages();
          if (typeof restoreInputValue === "string") {
            setInput(restoreInputValue);
          }
          setError(payload.error ?? "Chat response was empty.");
          return null;
        }

        accumulatedAssistantContent = payload.message.content;
        updateAssistant(accumulatedAssistantContent);

        const summary: SessionSummary = {
          id: session.id,
          createdAt:
            sessions.find((item) => item.id === session.id)?.createdAt ??
            new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          model: selectedModel
            ? { id: selectedModel.id, displayName: selectedModel.displayName }
            : session.model,
          messageCount: requestSnapshot.length + 1,
          lastMessagePreview: accumulatedAssistantContent.trim() || null,
        };
        upsertSessionSummary(summary);
        return accumulatedAssistantContent;
      }

      const reader = response.body.getReader();
      readerRef.current = reader;
      const textDecoder = new TextDecoder();
      let buffer = "";
      let completed = false;

      while (!completed) {
        const { done, value } = await reader.read();
        if (done) break;
        if (!value) continue;

        buffer += textDecoder.decode(value, { stream: true });
        const segments = buffer.split("\n");
        buffer = segments.pop() ?? "";

        for (const segment of segments) {
          const trimmedSegment = segment.trim();
          if (!trimmedSegment) continue;

          let chunk: StreamChunk | null = null;
          try {
            chunk = JSON.parse(trimmedSegment) as StreamChunk;
          } catch (serializationError) {
            console.error("Failed to parse chat stream chunk", serializationError);
            continue;
          }

          if (chunk.type === "delta") {
            accumulatedAssistantContent += chunk.content;
            updateAssistant(accumulatedAssistantContent);
          } else if (chunk.type === "error") {
            removeAssistant();
            revertMessages();
            if (typeof restoreInputValue === "string") {
              setInput(restoreInputValue);
            }
            setError(chunk.message ?? "Chat stream failed.");
            completed = true;
            break;
          } else if (chunk.type === "done") {
            completed = true;
            break;
          }
        }
      }

      if (!completed && buffer.trim()) {
        try {
          const chunk = JSON.parse(buffer.trim()) as StreamChunk;
          if (chunk.type === "delta") {
            accumulatedAssistantContent += chunk.content;
            updateAssistant(accumulatedAssistantContent);
          } else if (chunk.type === "error") {
            removeAssistant();
            revertMessages();
            if (typeof restoreInputValue === "string") {
              setInput(restoreInputValue);
            }
            setError(chunk.message ?? "Chat stream failed.");
            return null;
          }
        } catch (serializationError) {
          console.error("Failed to parse trailing chat chunk", serializationError);
        }
      }

      updateAssistant(accumulatedAssistantContent);

      const summary: SessionSummary = {
        id: session.id,
        createdAt:
          sessions.find((item) => item.id === session.id)?.createdAt ??
          new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        model: selectedModel
          ? { id: selectedModel.id, displayName: selectedModel.displayName }
          : session.model,
        messageCount: requestSnapshot.length + 1,
        lastMessagePreview: accumulatedAssistantContent.trim() || null,
      };
      upsertSessionSummary(summary);

      return accumulatedAssistantContent;
    } catch (chatError) {
      const errorName = (chatError as Error)?.name;
      if (errorName === "AbortError" || errorName === "TimeoutError") {
        const finalContent = accumulatedAssistantContent.trim();
        if (finalContent.length === 0) {
          if (revertOnAbortWhenEmpty) {
            revertMessages();
          } else {
            removeAssistant();
          }
        } else {
          updateAssistant(finalContent);
        }
        if (typeof restoreInputValue === "string") {
          setInput(restoreInputValue);
        }
        setError(
          errorName === "TimeoutError" ? "Generation timed out." : "Generation cancelled.",
        );
      } else {
        console.error("Chat request error", chatError);
        removeAssistant();
        revertMessages();
        if (typeof restoreInputValue === "string") {
          setInput(restoreInputValue);
        }
        setError("Unable to reach chat service.");
      }
      return null;
    } finally {
      readerRef.current = null;
      abortControllerRef.current = null;
    }
  };

  const startNewChat = async (): Promise<ChatSessionData | null> => {
    if (!selectedModelId) {
      setError("Select a chat model before starting a new chat.");
      return null;
    }

    setIsSessionLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/characters/${character.slug}/sessions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          greeting: greetings[greetingIndex] ?? "",
          modelId: selectedModelId,
        }),
      });

      const payload = await response.json();

      if (!response.ok || !payload?.chatSession) {
        setError(payload?.error ?? "Failed to start a new chat.");
        return null;
      }

      const sessionPayload = payload.chatSession as ApiChatSession;
      const sessionMessages = Array.isArray(sessionPayload.messages)
        ? toChatMessages(sessionPayload.messages)
        : [];

      const session: ChatSessionData = {
        id: String(sessionPayload.id),
        messages: sessionMessages,
        model: sessionPayload.model
          ? {
              id: sessionPayload.model.id,
              displayName: sessionPayload.model.displayName,
            }
          : null,
      };

      setChatSessionId(session.id);
      setMessages(session.messages.length > 0 ? session.messages : []);
      setEditingMessageId(null);
      setEditingContent("");
      setSelectedModelId(session.model?.id ?? selectedModelId ?? defaultModelId);
      upsertSessionSummary(
        resolveSessionSummary(sessionPayload, sessionMessages),
      );
      return session;
    } catch (requestError) {
      console.error("Start new chat failed", requestError);
      setError("Unable to start a new chat.");
      return null;
    } finally {
      setIsSessionLoading(false);
    }
  };

  const loadChatSession = async (sessionId: string) => {
    setIsSessionLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/characters/${character.slug}/sessions/${sessionId}`,
      );

      const payload = await response.json();

      if (!response.ok || !payload?.chatSession) {
      setError(payload?.error ?? "Failed to load chat session.");
      return;
      }

      const sessionPayload = payload.chatSession as ApiChatSession;
      const sessionMessages = Array.isArray(sessionPayload.messages)
        ? toChatMessages(sessionPayload.messages)
        : [];

      setChatSessionId(String(sessionPayload.id));
      setMessages(sessionMessages);
      setInput("");
      setEditingMessageId(null);
      setEditingContent("");
      if (sessionPayload.model?.id) {
        setSelectedModelId(sessionPayload.model.id);
      } else {
        setSelectedModelId((current) => current ?? defaultModelId);
      }
      upsertSessionSummary(
        resolveSessionSummary(sessionPayload, sessionMessages),
      );
    } catch (requestError) {
      console.error("Load chat session failed", requestError);
      setError("Unable to load chat session.");
    } finally {
      setIsSessionLoading(false);
    }
  };

  const handleSessionChange = async (
    event: FormEvent<HTMLSelectElement>,
  ) => {
    const target = event.currentTarget;
    const selectedId = target.value;

    if (!selectedId) {
      setChatSessionId(null);
      const greeting = greetings[0];
      setGreetingIndex(0);
      setMessages(
        greeting
          ? [
              {
                id: "assistant-preview",
                role: "assistant",
                content: greeting,
              },
            ]
          : [],
      );
      setInput("");
      setEditingMessageId(null);
      setEditingContent("");
      setSelectedModelId(defaultModelId);
      return;
    }

    if (selectedId === chatSessionId) {
      return;
    }

    const summary = sessions.find((session) => session.id === selectedId);
    if (summary?.model?.id) {
      setSelectedModelId(summary.model.id);
    }

    await loadChatSession(selectedId);
  };

  const handleModelChange = (event: FormEvent<HTMLSelectElement>) => {
    const value = event.currentTarget.value;
    const modelId = value.length ? value : null;

    const nextModelId = modelId ?? defaultModelId;

    setSelectedModelId(nextModelId);

    if (chatSessionId && nextModelId) {
      const option = models.find((model) => model.id === nextModelId);
      if (option) {
        setSessions((current) =>
          current.map((session) =>
            session.id === chatSessionId
              ? {
                  ...session,
                  model: {
                    id: option.id,
                    displayName: option.displayName,
                  },
                }
              : session,
          ),
        );
      }
    }
  };

  const handleStartNewChat = async () => {
    await startNewChat();
  };

  const handleDeleteChat = async () => {
    if (!chatSessionId) {
      const greeting = greetings[0];
      setGreetingIndex(0);
      setMessages(
        greeting
          ? [
              {
                id: "assistant-preview",
                role: "assistant",
                content: greeting,
              },
            ]
          : [],
      );
      setInput("");
      setError(null);
      setEditingMessageId(null);
      setEditingContent("");
      setSelectedModelId(defaultModelId);
      return;
    }

    setIsSessionLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/characters/${character.slug}/sessions/${chatSessionId}`,
        {
          method: "DELETE",
        },
      );

      let payload: { error?: string } | undefined;

      try {
        payload = await response.json();
      } catch {
        // ignore JSON parse errors
      }

      if (!response.ok) {
        setError(payload?.error ?? "Failed to delete chat.");
        return;
      }

      const remainingSessions = sessions.filter(
        (session) => session.id !== chatSessionId,
      );

      removeSessionSummary(chatSessionId);

      if (remainingSessions.length > 0) {
        await loadChatSession(remainingSessions[0].id);
      } else {
        setChatSessionId(null);
        const greeting = greetings[0];
        setGreetingIndex(0);
        setMessages(
          greeting
            ? [
                {
                  id: "assistant-preview",
                  role: "assistant",
                  content: greeting,
                },
              ]
            : [],
        );
        setInput("");
        setEditingMessageId(null);
        setEditingContent("");
        setSelectedModelId(defaultModelId);
      }
    } catch (requestError) {
      console.error("Delete chat failed", requestError);
      setError("Unable to delete chat.");
    } finally {
      setIsSessionLoading(false);
    }
  };

  const beginEditingMessage = (message: ChatMessage) => {
    if (isSubmitting || isSessionLoading || isMessageUpdating) {
      return;
    }

    if (!chatSessionId || message.id === "assistant-preview") {
      return;
    }

    setEditingMessageId(message.id);
    setEditingContent(message.content);
    setError(null);
  };

  const handleCancelEdit = () => {
    if (isMessageUpdating) {
      return;
    }

    setEditingMessageId(null);
    setEditingContent("");
    setError(null);
  };

  const handleSaveEditedMessage = async () => {
    if (!editingMessageId || !chatSessionId) {
      return;
    }

    if (isMessageUpdating) {
      return;
    }

    const target = messages.find((message) => message.id === editingMessageId);

    if (!target) {
      setEditingMessageId(null);
      setEditingContent("");
      return;
    }

    const trimmed = editingContent.trim();
    if (trimmed.length === 0) {
      setError("Message cannot be empty.");
      return;
    }

    if (trimmed === target.content.trim()) {
      setEditingMessageId(null);
      setEditingContent("");
      return;
    }

    const isLastMessage = messages[messages.length - 1]?.id === editingMessageId;

    setIsMessageUpdating(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/characters/${character.slug}/sessions/${chatSessionId}/messages/${editingMessageId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ content: trimmed }),
        },
      );

      let payload:
        | {
            chatMessage?: { id: string; role: Role; content: string };
            chatSession?: { id: string; updatedAt: string };
            error?: string;
          }
        | undefined;

      try {
        payload = (await response.json()) as typeof payload;
      } catch {
        // ignore JSON parse failures
      }

      if (!response.ok || !payload?.chatMessage) {
        setError(payload?.error ?? "Failed to update message.");
        return;
      }

      const updatedContent = payload.chatMessage.content;
      const updatedAt = payload.chatSession?.updatedAt ?? new Date().toISOString();

      setMessages((current) =>
        current.map((message) =>
          message.id === editingMessageId ? { ...message, content: updatedContent } : message,
        ),
      );

      setSessions((current) =>
        sortSummaries(
          current.map((session) =>
            session.id === chatSessionId
              ? {
                  ...session,
                  updatedAt,
                  lastMessagePreview: isLastMessage
                    ? updatedContent.trim() || null
                    : session.lastMessagePreview,
                }
              : session,
          ),
        ),
      );

      setEditingMessageId(null);
      setEditingContent("");
    } catch (requestError) {
      console.error("Update chat message failed", requestError);
      setError("Unable to update the message.");
    } finally {
      setIsMessageUpdating(false);
    }
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    const trimmed = input.trim();
    if (!trimmed || isSubmitting) {
      return;
    }

    if (editingMessageId) {
      setError("Finish editing your message before sending a new one.");
      return;
    }

    if (!selectedModelId) {
      setError("Select a chat model before chatting.");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      let session: ChatSessionData | null;

      if (chatSessionId) {
        session = {
          id: chatSessionId,
          messages: messages.map((message) => ({ ...message })),
          model: selectedModel
            ? { id: selectedModel.id, displayName: selectedModel.displayName }
            : null,
        };
      } else {
        session = await startNewChat();
      }

      if (!session) {
        return;
      }

      const baseMessages = session.messages.map((message) => ({ ...message }));

      const userMessage: ChatMessage = {
        id: createMessageId(),
        role: "user",
        content: trimmed,
      };

      const history = [...baseMessages, userMessage];

      setMessages(history);
      setEditingMessageId(null);
      setEditingContent("");
      setInput("");

      const result = await sendChatRequest({
        session,
        requestMessages: history,
        restoreInputValue: trimmed,
        revertMessages: () => {
          setMessages(baseMessages);
        },
      });

      if (result !== null) {
        await refreshSessionFromServer(String(session.id));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRegenerate = async () => {
    if (isSubmitting || isSessionLoading) {
      return;
    }

    if (editingMessageId) {
      setError("Finish editing your message before regenerating.");
      return;
    }

    if (!chatSessionId) {
      return;
    }

    if (!selectedModelId) {
      setError("Select a chat model before regenerating.");
      return;
    }

    const currentMessages = messages.map((message) => ({ ...message }));
    if (currentMessages.length === 0) {
      return;
    }

    const lastMessage = currentMessages[currentMessages.length - 1];
    if (lastMessage.role !== "assistant") {
      return;
    }

    let lastUserIndex = -1;
    for (let index = currentMessages.length - 2; index >= 0; index -= 1) {
      if (currentMessages[index].role === "user") {
        lastUserIndex = index;
        break;
      }
    }

    if (lastUserIndex === -1) {
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const sessionSummary = sessions.find((session) => session.id === chatSessionId);

      const session: ChatSessionData = {
        id: chatSessionId,
        messages: currentMessages,
        model: selectedModel
          ? { id: selectedModel.id, displayName: selectedModel.displayName }
          : sessionSummary?.model ?? null,
      };

      const assistantMessage = { ...currentMessages[currentMessages.length - 1] };
      const requestMessages = currentMessages
        .slice(0, currentMessages.length - 1)
        .map((message) => ({ ...message }));
      const precedingUserMessage = requestMessages[requestMessages.length - 1];

      setMessages((existing) =>
        existing.map((message) =>
          message.id === assistantMessage.id ? { ...message, content: "" } : message,
        ),
      );

      const result = await sendChatRequest({
        session,
        requestMessages,
        assistantMessageId: assistantMessage.id,
        revertOnAbortWhenEmpty: true,
        revertMessages: () => {
          setMessages([...requestMessages, assistantMessage]);
        },
        reuseUserMessageId:
          precedingUserMessage?.role === "user" ? precedingUserMessage.id ?? null : null,
        replaceAssistantMessageId: assistantMessage.id,
      });

      if (result !== null) {
        await refreshSessionFromServer(String(session.id));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const actionsDisabled =
    isSessionLoading || isSubmitting || isMessageUpdating || Boolean(editingMessageId);
  const hasSavedSessions = sessions.length > 0;
  const modelSelectValue = selectedModelId ?? "";
  const modelSelectDisabled = actionsDisabled || !hasModels;

  const selectedModelPricing = useMemo(() => {
    if (!selectedModel) {
      return "";
    }

    const priceBits: string[] = [];

    if (typeof selectedModel.promptPricePer1MTokens === "number") {
      priceBits.push(
        `input ${selectedModel.currency} ${selectedModel.promptPricePer1MTokens}/1M`,
      );
    }

    if (typeof selectedModel.completionPricePer1MTokens === "number") {
      priceBits.push(
        `output ${selectedModel.currency} ${selectedModel.completionPricePer1MTokens}/1M`,
      );
    }

    return priceBits.join(" • ");
  }, [selectedModel]);

  const sessionSelectValue = useMemo(
    () => chatSessionId ?? "",
    [chatSessionId],
  );

  const greetingsCount = greetings.length;
  const canCycleGreetings = !chatSessionId && greetingsCount > 1;

  const canRegenerate = useMemo(() => {
    if (!chatSessionId || !selectedModelId || editingMessageId) {
      return false;
    }
    if (isSubmitting || isSessionLoading || isMessageUpdating) {
      return false;
    }
    if (messages.length === 0) {
      return false;
    }
    const last = messages[messages.length - 1];
    if (last.role !== "assistant") {
      return false;
    }
    for (let index = messages.length - 2; index >= 0; index -= 1) {
      if (messages[index].role === "user") {
        return true;
      }
    }
    return false;
  }, [
    chatSessionId,
    editingMessageId,
    isMessageUpdating,
    isSessionLoading,
    isSubmitting,
    messages,
    selectedModelId,
  ]);

  const creatorNotesSummary = useMemo(() => {
    const raw = character.creatorNotes?.trim();
    if (!raw) {
      return "No creator notes provided.";
    }
    if (raw.length <= 160) {
      return raw;
    }
    return `${raw.slice(0, 160)}…`;
  }, [character.creatorNotes]);

  const handleStop = () => {
    readerRef.current?.cancel().catch(() => undefined);
    abortControllerRef.current?.abort();
  };

  const goToPreviousGreeting = () => {
    if (!canCycleGreetings) return;
    setGreetingIndex((index) =>
      index === 0 ? greetingsCount - 1 : index - 1,
    );
  };

  const goToNextGreeting = () => {
    if (!canCycleGreetings) return;
    setGreetingIndex((index) =>
      index === greetingsCount - 1 ? 0 : index + 1,
    );
  };

  return (
    <div className="flex h-full flex-col rounded-lg border border-neutral-200 bg-white shadow-sm lg:h-[calc(100vh-8rem)]">
      <div className="flex flex-wrap items-center justify-between gap-3 border-b border-neutral-200 px-6 py-4">
        <div className="flex items-center gap-3">
          <div className="flex h-12 w-12 items-center justify-center overflow-hidden rounded-full bg-neutral-100 text-lg font-semibold text-neutral-700">
            {character.avatarUrl ? (
              // eslint-disable-next-line @next/next/no-img-element
              <img
                src={character.avatarUrl}
                alt={`${character.name} avatar`}
                className="h-full w-full object-cover"
                referrerPolicy="no-referrer"
              />
            ) : (
              character.name.charAt(0).toUpperCase()
            )}
          </div>
          <div className="flex flex-col">
            <p className="text-base font-semibold text-neutral-900">
              {character.name}
            </p>
            <p className="text-xs text-neutral-500">
              Version {character.characterVersion}
            </p>
          </div>
        </div>
        <div className="flex flex-col items-stretch justify-end gap-3 sm:flex-row sm:items-center sm:gap-4">
          <div className="flex flex-wrap items-center gap-2">
            <label
              htmlFor="model-select"
              className="text-xs font-medium text-neutral-500"
            >
              Model
            </label>
            <select
              id="model-select"
              value={modelSelectValue}
              onChange={handleModelChange}
              disabled={modelSelectDisabled}
              className="rounded border border-neutral-300 bg-white px-2 py-1 text-xs text-neutral-700 focus:border-neutral-500 focus:outline-none disabled:cursor-not-allowed disabled:bg-neutral-100"
            >
              {hasModels ? (
                models.map((model) => (
                  <option key={model.id} value={model.id}>
                    {model.displayName} ({model.providerName})
                  </option>
                ))
              ) : (
                <option value="">No models configured</option>
              )}
            </select>
            {selectedModel ? (
              <span className="text-[10px] text-neutral-500">
                {selectedModel.providerName}
                {selectedModelPricing ? ` • ${selectedModelPricing}` : ""}
              </span>
            ) : null}
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <label
              htmlFor="session-select"
              className="text-xs font-medium text-neutral-500"
            >
              Session
            </label>
            <select
              id="session-select"
              value={sessionSelectValue}
              onChange={handleSessionChange}
              disabled={actionsDisabled || !hasSavedSessions}
              className="rounded border border-neutral-300 bg-white px-2 py-1 text-xs text-neutral-700 focus:border-neutral-500 focus:outline-none disabled:cursor-not-allowed disabled:bg-neutral-100"
            >
              <option value="" disabled={!hasSavedSessions}>
                {hasSavedSessions ? "Select a session" : "No saved sessions"}
              </option>
              {sessions.map((session) => (
                <option key={session.id} value={session.id}>
                  {formatSessionLabel(session)}
                </option>
              ))}
            </select>
            <button
              type="button"
              onClick={handleStartNewChat}
              disabled={actionsDisabled || !hasModels || !selectedModelId}
              className="inline-flex items-center justify-center rounded border border-neutral-300 px-3 py-1.5 text-xs font-medium text-neutral-700 transition hover:border-neutral-400 disabled:cursor-not-allowed disabled:opacity-60"
            >
              {isSessionLoading && !chatSessionId ? "Creating…" : "Start New Chat"}
            </button>
            <button
              type="button"
              onClick={handleDeleteChat}
              disabled={actionsDisabled || !chatSessionId}
              className="inline-flex items-center justify-center rounded border border-neutral-300 px-3 py-1.5 text-xs font-medium text-neutral-700 transition hover:border-neutral-400 disabled:cursor-not-allowed disabled:opacity-60"
            >
              Delete Chat
            </button>
          </div>
        </div>
        {!hasModels ? (
          <p className="text-xs text-red-600">
            No chat models are available. Configure one in the admin tools to begin chatting.
          </p>
        ) : null}
      </div>

      <div className="border-b border-neutral-200 bg-neutral-50 px-6 py-4 text-xs text-neutral-600">
        <div className="flex flex-wrap items-center justify-between gap-3">
          <p className="max-w-2xl whitespace-pre-wrap text-neutral-600">
            {creatorNotesSummary}
          </p>
          <div className="flex flex-wrap items-center gap-3 text-xs font-medium text-neutral-700">
            {greetingsCount > 1 ? (
              <div className="inline-flex items-center gap-2">
                <button
                  type="button"
                  onClick={goToPreviousGreeting}
                  disabled={!canCycleGreetings}
                  className="rounded border border-neutral-300 px-2 py-1 text-xs transition hover:border-neutral-400 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  ←
                </button>
                <span>
                  Greeting {greetingIndex + 1} of {greetingsCount}
                </span>
                <button
                  type="button"
                  onClick={goToNextGreeting}
                  disabled={!canCycleGreetings}
                  className="rounded border border-neutral-300 px-2 py-1 text-xs transition hover:border-neutral-400 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  →
                </button>
              </div>
            ) : null}
            <label className="inline-flex items-center gap-2">
              <input
                type="checkbox"
                className="h-4 w-4"
                checked={includeBook}
                onChange={(event) => setIncludeBook(event.currentTarget.checked)}
                disabled={actionsDisabled}
              />
              Include character book in responses
            </label>
          </div>
        </div>

        <details className="mt-3 space-y-2">
          <summary className="cursor-pointer text-neutral-700">
            Show creator notes
          </summary>
          <p className="whitespace-pre-wrap text-neutral-600">
            {character.creatorNotes?.trim() || "No creator notes provided."}
          </p>
        </details>
      </div>

      <div
        ref={listRef}
        className="flex-1 space-y-4 overflow-y-auto px-6 py-6 text-sm text-neutral-800"
      >
        {messages.length === 0 ? (
          <p className="text-center text-neutral-500">
            This character card does not include a starting message. Start a new
            chat to begin the conversation.
          </p>
        ) : null}

        {messages.map((message) => {
          const isUserMessage = message.role === "user";
          const isEditing = editingMessageId === message.id;
          const canEditMessage = Boolean(chatSessionId) && message.id !== "assistant-preview";
          const editLabel = isUserMessage ? "Edit" : "Edit response";
          return (
            <div
              key={message.id}
              className={`flex ${isUserMessage ? "justify-end" : "justify-start"}`}
            >
              <div
                className={`flex w-full max-w-[75%] flex-col gap-1 ${
                  isUserMessage ? "items-end" : "items-start"
                }`}
              >
                {isEditing ? (
                  <textarea
                    autoFocus
                    value={editingContent}
                    onChange={(event) => setEditingContent(event.currentTarget.value)}
                    rows={Math.min(10, Math.max(3, editingContent.split("\n").length))}
                    disabled={isMessageUpdating}
                    className="w-full resize-y rounded border border-neutral-300 bg-white px-3 py-2 text-sm text-neutral-900 shadow-sm focus:border-neutral-500 focus:outline-none disabled:cursor-not-allowed disabled:bg-neutral-100"
                  />
                ) : (
                  <div
                    className={`w-fit max-w-full rounded-lg px-4 py-3 shadow-sm ${
                      isUserMessage
                        ? "bg-black text-white"
                        : "bg-neutral-100 text-neutral-900"
                    } ${isUserMessage ? "self-end" : "self-start"}`}
                  >
                    {isUserMessage ? (
                      <div className="whitespace-pre-wrap text-sm leading-relaxed">
                        {message.content || ""}
                      </div>
                    ) : (
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        rehypePlugins={[rehypeHighlightQuotes]}
                        components={assistantMarkdownComponents}
                      >
                        {message.content?.length ? message.content : "…"}
                      </ReactMarkdown>
                    )}
                  </div>
                )}
                {canEditMessage ? (
                  <div
                    className={`flex gap-2 text-[11px] text-neutral-500 ${
                      isUserMessage ? "justify-end" : "justify-start"
                    }`}
                  >
                    {isEditing ? (
                      <>
                        <button
                          type="button"
                          onClick={handleSaveEditedMessage}
                          disabled={
                            isMessageUpdating || editingContent.trim().length === 0
                          }
                          className="rounded border border-neutral-300 px-2 py-0.5 transition hover:border-neutral-400 disabled:cursor-not-allowed disabled:opacity-60"
                        >
                          Save
                        </button>
                        <button
                          type="button"
                          onClick={handleCancelEdit}
                          disabled={isMessageUpdating}
                          className="rounded border border-neutral-200 px-2 py-0.5 transition hover:border-neutral-300 disabled:cursor-not-allowed disabled:opacity-60"
                        >
                          Cancel
                        </button>
                      </>
                    ) : (
                      <button
                        type="button"
                        onClick={() => beginEditingMessage(message)}
                        disabled={
                          isSubmitting ||
                          isSessionLoading ||
                          isMessageUpdating ||
                          (editingMessageId !== null && editingMessageId !== message.id)
                        }
                        className="rounded border border-transparent px-2 py-0.5 transition hover:border-neutral-300 hover:text-neutral-700 disabled:cursor-not-allowed disabled:text-neutral-300"
                      >
                        {editLabel}
                      </button>
                    )}
                  </div>
                ) : null}
              </div>
            </div>
          );
        })}
      </div>

      {error ? (
        <div className="px-6 pb-2">
          <div className="rounded border border-red-200 bg-red-50 px-3 py-2 text-xs text-red-700">
            {error}
          </div>
        </div>
      ) : null}

      <form onSubmit={handleSubmit} className="border-t border-neutral-200 px-6 py-4">
        <label className="block text-xs font-medium text-neutral-600">
          Your message
        </label>
        <textarea
          value={input}
          onChange={(event) => setInput(event.currentTarget.value)}
          rows={3}
          placeholder={
            chatSessionId
              ? "Type your reply..."
              : "Start a new chat or type a message to begin."
          }
          className="mt-2 w-full resize-none rounded border border-neutral-300 px-3 py-2 text-sm focus:border-neutral-500 focus:outline-none"
        />
        <div className="mt-3 flex items-center justify-end gap-3">
          <button
            type="button"
            onClick={handleRegenerate}
            disabled={!canRegenerate}
            className="inline-flex items-center justify-center rounded border border-neutral-300 px-4 py-2 text-sm font-medium text-neutral-700 transition hover:border-neutral-400 disabled:cursor-not-allowed disabled:opacity-60"
          >
            Regenerate
          </button>
          <button
            type={isSubmitting ? "button" : "submit"}
            onClick={isSubmitting ? handleStop : undefined}
            disabled={isSessionLoading || Boolean(editingMessageId) || isMessageUpdating}
            className={`inline-flex items-center justify-center rounded px-4 py-2 text-sm font-medium transition ${
              isSubmitting
                ? "border border-neutral-300 text-neutral-700 hover:border-neutral-400"
                : "bg-black text-white disabled:cursor-not-allowed disabled:opacity-60"
            }`}
          >
            {isSubmitting ? "Stop" : "Send"}
          </button>
        </div>
      </form>
    </div>
  );
}
