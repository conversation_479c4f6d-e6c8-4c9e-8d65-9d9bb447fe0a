import { NextResponse } from "next/server";

import { Prisma } from "@/generated/prisma";
import { prisma } from "@/lib/prisma";
import { auth } from "@/auth";

type RouteParams = {
  params: Promise<{
    providerId: string;
  }>;
};

const parseMetadata = (value: unknown): Prisma.JsonValue | null => {
  if (typeof value === "undefined") {
    return null;
  }

  if (value === null) {
    return null;
  }

  if (typeof value === "string") {
    const trimmed = value.trim();
    if (!trimmed) {
      return null;
    }

    try {
      return JSON.parse(trimmed) as Prisma.JsonValue;
    } catch {
      throw new Error("Metadata must be valid JSON.");
    }
  }

  if (typeof value === "object") {
    return value as Prisma.JsonValue;
  }

  throw new Error("Metadata must be JSON.");
};

export async function PATCH(request: Request, { params }: RouteParams) {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  if (session.user.role !== "ADMIN") {
    return NextResponse.json({ error: "Forbidden." }, { status: 403 });
  }

  const { providerId } = await params;

  let payload: unknown;
  try {
    payload = await request.json();
  } catch {
    return NextResponse.json({ error: "Body must be valid JSON." }, { status: 400 });
  }

  if (!payload || typeof payload !== "object") {
    return NextResponse.json({ error: "Body must be an object." }, { status: 400 });
  }

  const { name, baseUrl, apiKey, metadata } = payload as {
    name?: unknown;
    baseUrl?: unknown;
    apiKey?: unknown;
    metadata?: unknown;
  };

  const data: Prisma.LLMProviderUpdateInput = {};

  if (typeof name !== "undefined") {
    if (typeof name !== "string" || name.trim().length === 0) {
      return NextResponse.json({ error: "Provider name must be a non-empty string." }, { status: 400 });
    }
    data.name = name.trim();
  }

  if (typeof baseUrl !== "undefined") {
    if (baseUrl === null) {
      data.baseUrl = null;
    } else if (typeof baseUrl === "string") {
      const trimmed = baseUrl.trim();
      data.baseUrl = trimmed.length ? trimmed : null;
    } else {
      return NextResponse.json({ error: "Base URL must be a string." }, { status: 400 });
    }
  }

  if (typeof apiKey !== "undefined") {
    if (apiKey === null) {
      data.apiKey = null;
    } else if (typeof apiKey === "string") {
      const trimmed = apiKey.trim();
      data.apiKey = trimmed.length ? trimmed : null;
    } else {
      return NextResponse.json({ error: "API key must be a string." }, { status: 400 });
    }
  }

  if (typeof metadata !== "undefined") {
    try {
      const parsedMetadata = parseMetadata(metadata);
      data.metadata = parsedMetadata === null ? Prisma.DbNull : parsedMetadata;
    } catch (error) {
      return NextResponse.json(
        { error: error instanceof Error ? error.message : "Metadata must be valid JSON." },
        { status: 400 },
      );
    }
  }

  try {
    const updated = await prisma.lLMProvider.update({
      where: { id: providerId },
      data,
      include: {
        models: {
          orderBy: [{ isDefault: "desc" }, { displayName: "asc" }],
        },
      },
    });

    return NextResponse.json({ success: true, providerId: updated.id });
  } catch (error) {
    console.error("Failed to update LLM provider", error);
    return NextResponse.json({ error: "Failed to update provider." }, { status: 500 });
  }
}

export async function DELETE(_: Request, { params }: RouteParams) {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  if (session.user.role !== "ADMIN") {
    return NextResponse.json({ error: "Forbidden." }, { status: 403 });
  }

  const { providerId } = await params;

  try {
    await prisma.lLMProvider.delete({ where: { id: providerId } });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to delete LLM provider", error);
    return NextResponse.json({ error: "Failed to delete provider." }, { status: 500 });
  }
}
