import { NextResponse } from "next/server";

import type { LLMModel } from "@/generated/prisma";
import { MessageRole } from "@/generated/prisma";
import { getCharacterForRead } from "@/lib/permissions";
import { prisma } from "@/lib/prisma";
import {
  getCurrentUserPersona,
  replacePersonaPlaceholders,
  resolvePersonaStrings,
} from "@/lib/userPersona";
import { auth } from "@/auth";

type RouteParams = {
  params: Promise<{
    slug: string;
    sessionId: string;
  }>;
};

const mapRole = (role: MessageRole): "assistant" | "user" =>
  role === MessageRole.ASSISTANT ? "assistant" : "user";

const buildPayload = (
  session: {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    model: Pick<LLMModel, "id" | "displayName"> | null;
    messages: Array<{
      id: string;
      role: MessageRole;
      content: string;
      createdAt: Date;
    }>;
  } | null,
) => {
  if (!session) {
    return null;
  }

  return {
    chatSession: {
      id: session.id,
      createdAt: session.createdAt.toISOString(),
      updatedAt: session.updatedAt.toISOString(),
      model: session.model
        ? { id: session.model.id, displayName: session.model.displayName }
        : null,
      messages: session.messages
        .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
        .map((message) => ({
          id: message.id,
          role: mapRole(message.role),
          content: message.content,
          createdAt: message.createdAt.toISOString(),
        })),
    },
  };
};

export async function GET(_: Request, { params }: RouteParams) {
  const { slug, sessionId } = await params;

  const userSession = await auth();

  if (!userSession?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  const character = await getCharacterForRead({
    slug,
    select: {
      id: true,
    },
  });

  if (!character) {
    return NextResponse.json({ error: "Character not found." }, { status: 404 });
  }

  const chatSession = await prisma.chatSession.findFirst({
    where: {
      id: sessionId,
      characterId: character.id,
    },
    include: {
      messages: true,
      model: {
        select: {
          id: true,
          displayName: true,
        },
      },
    },
  });

  if (!chatSession) {
    return NextResponse.json({ error: "Chat session not found." }, { status: 404 });
  }

  const userPersona = await getCurrentUserPersona({
    userId: userSession.user.id!,
    userName: userSession.user.name,
  });

  const personaStrings = resolvePersonaStrings(userPersona);

  const adjustedSession = {
    ...chatSession,
    messages: chatSession.messages.map((message) =>
      message.role === MessageRole.ASSISTANT
        ? {
            ...message,
            content:
              replacePersonaPlaceholders(message.content, personaStrings) ?? message.content,
          }
        : message,
    ),
  };

  const payload = buildPayload(adjustedSession);

  return NextResponse.json(payload);
}

export async function DELETE(_: Request, { params }: RouteParams) {
  const { slug, sessionId } = await params;

  const userSession = await auth();

  if (!userSession?.user) {
    return NextResponse.json({ error: "Not authenticated." }, { status: 401 });
  }

  const character = await getCharacterForRead({
    slug,
    select: {
      id: true,
    },
  });

  if (!character) {
    return NextResponse.json({ error: "Character not found." }, { status: 404 });
  }

  const chatSession = await prisma.chatSession.findUnique({
    where: { id: sessionId },
    select: {
      id: true,
      characterId: true,
    },
  });

  if (!chatSession || chatSession.characterId !== character.id) {
    return NextResponse.json({ error: "Chat session not found." }, { status: 404 });
  }

  await prisma.chatSession.delete({
    where: { id: sessionId },
  });

  return NextResponse.json({ ok: true });
}
