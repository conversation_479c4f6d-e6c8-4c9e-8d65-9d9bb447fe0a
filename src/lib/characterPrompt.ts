import type { Prisma } from "@/generated/prisma";

import type { EvaluatedBook } from "./characterBook";
import type { PersonaStrings } from "./userPersona";
import { replacePersonaPlaceholders } from "./userPersona";

type CharacterLike = {
  name: string;
  description: string | null;
  personality: string | null;
  scenario: string | null;
  creatorNotes: string | null;
  spec: string | null;
  rawData: Prisma.JsonValue;
  characterBook: Prisma.JsonValue | null;
  postHistoryInstructions: string | null;
};

type CardData = {
  system_prompt?: string;
  post_history_instructions?: string;
  mes_example?: string;
};

type RawCharacterCard = {
  data?: CardData & Record<string, unknown>;
};

const joinSections = (sections: Array<string | null | undefined>) =>
  sections
    .map((section) => (section ? section.trim() : ""))
    .filter(Boolean)
    .join("\n\n");

const DEFAULT_BOOK_DEPTH = 4;

export const buildCharacterSystemPrompt = (
  character: CharacterLike,
  options?: {
    book?: EvaluatedBook | null;
    persona?: PersonaStrings | null;
  },
) => {
  const raw = (character.rawData as RawCharacterCard | null) ?? null;
  const cardData: CardData | null =
    raw && typeof raw === "object" && raw?.data && typeof raw.data === "object"
      ? (raw.data as CardData)
      : null;

  const header = `You are ${character.name}, a fictional character participating in an immersive, consented roleplay chat. Always respond in the voice, tone, and knowledge of ${character.name}.`;

  const applyUserPersona = <T extends string | null | undefined>(value: T) =>
    options?.persona && typeof value === "string"
      ? (replacePersonaPlaceholders(value, options.persona) as T)
      : value;

  const personaDetails = joinSections([
    character.description ? `Description:\n${character.description}` : null,
    character.personality ? `Personality:\n${character.personality}` : null,
    character.scenario ? `Scenario:\n${character.scenario}` : null,
    character.creatorNotes ? `Creator Notes:\n${character.creatorNotes}` : null,
  ].map((section) => applyUserPersona(section)));

  const cardSpec = character.spec
    ? applyUserPersona(`Character Card Spec:\n${character.spec}`)
    : null;

  const systemPrompt = cardData?.system_prompt
    ? applyUserPersona(`System Prompt:\n${cardData.system_prompt}`)
    : null;

  const historySource =
    character.postHistoryInstructions ?? cardData?.post_history_instructions ?? null;

  const historyInstructions = historySource
    ? applyUserPersona(`Conversation Guidance:\n${historySource}`)
    : null;

  const example = cardData?.mes_example
    ? applyUserPersona(`Example Dialogue:\n${cardData.mes_example}`)
    : null;

  const bookSections: string[] = [];

  if (options?.book) {
    const bookJson =
      character.characterBook && typeof character.characterBook === "object"
        ? (character.characterBook as Record<string, unknown>)
        : null;
    const bookName =
      bookJson && typeof bookJson.name === "string" && bookJson.name.trim()
        ? bookJson.name.trim()
        : "Character Lore";

    if (options.book.before.length) {
      const beforeContent = options.book.before
        .map((entry) => applyUserPersona(entry) ?? entry)
        .join("\n");
      bookSections.push(
        `Character Book (${bookName}) — Before Chat\n${beforeContent}`,
      );
    }

    for (const depthEntry of options.book.depth) {
      const depthLabel = depthEntry.depth ?? DEFAULT_BOOK_DEPTH;
      const roleLabel = depthEntry.role;
      const depthContent = depthEntry.content
        .map((entry) => applyUserPersona(entry) ?? entry)
        .join("\n");
      bookSections.push(
        `Character Book (${bookName}) — Depth ${depthLabel}, Role ${roleLabel}\n${depthContent}`,
      );
    }

    if (options.book.after.length) {
      const afterContent = options.book.after
        .map((entry) => applyUserPersona(entry) ?? entry)
        .join("\n");
      bookSections.push(
        `Character Book (${bookName}) — After Chat\n${afterContent}`,
      );
    }
  }

  const bookSection = bookSections.length ? bookSections.join("\n\n") : null;

  const personaSection =
    options?.persona?.description && options.persona.description.trim().length > 0
      ? `User Persona:\n${options.persona.description}`
      : null;

  const remindersParts = [
    "Stay in character, avoid narrating the user's actions, and continue the roleplay unless explicitly instructed otherwise.",
  ];

  if (options?.book?.hasStatusFormat) {
    remindersParts.push(
      "If the character book defines a status_format, append the status block exactly as instructed after each reply.",
    );
  }

  const reminders = remindersParts.join(" ");

  return joinSections([
    header,
    personaDetails,
    cardSpec,
    systemPrompt,
    historyInstructions,
    example,
    bookSection,
    personaSection,
    reminders,
  ]);
};
